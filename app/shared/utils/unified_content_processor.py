"""
Unified Content Processing System for Socket Service, Followup Generation, and Editor Service

This module provides a shared content processing utility that handles:
- Task set formatting and storage
- Task item formatting and storage  
- Story formatting and storage
- Consistent data structures across all services

Only the initial AI generation (Gemini vs other models) remains different between services.
All formatting, storage, and processing logic is unified.
"""

import hashlib
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional, Literal, Tuple
from bson import ObjectId

from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from app.shared.db_enums import TaskStatus, QuizType, InputType, GenerationType, CollectionName

logger = setup_new_logging(__name__)


class UnifiedContentProcessor:
    """
    Unified content processing system for all services.
    
    This class handles consistent formatting and storage of tasks, stories, and task sets
    across socket service, followup generation, and editor service.
    """
    
    def __init__(self, current_user: UserTenantDB):
        """
        Initialize the unified content processor.
        
        Args:
            current_user: User context with database access
        """
        self.current_user = current_user
    
    def format_task_item(
        self,
        task_data: Dict[str, Any],
        task_set_id: ObjectId,
        processing_type: Literal['primary', 'followup', 'curated'],
        priority: int = 0
    ) -> Dict[str, Any]:
        """
        Format a task item with consistent structure across all services.
        
        Args:
            task_data: Raw task data from AI generation
            task_set_id: Parent task set ID
            processing_type: Type of processing ('primary', 'followup', or 'curated')
            priority: Task priority for ordering
            
        Returns:
            Formatted task item dictionary
        """
        task_id = ObjectId()
        task_type = task_data.get("type", "single_choice")
        
        # Standardize question format
        question_data = task_data.get("question", {})
        standardized_question = {
            "text": question_data.get("text", ""),
            "translated_text": question_data.get("translated_text", ""),
            "options": question_data.get("options", {}),
            "answer_hint": question_data.get("answer_hint", ""),
            "media_url": question_data.get("media_url"),
            "metadata": question_data.get("metadata", {}),
            "options_metadata": {}  # Will be populated during audio generation
        }
        
        # Standardize correct answer format
        correct_answer_data = self._format_correct_answer(task_data, task_type)
        
        # Create standardized task item
        task_item = {
            "_id": task_id,
            "task_set_id": task_set_id,
            "user_id": ObjectId(self.current_user.user.id),
            "type": task_type,
            "title": task_data.get("title", ""),
            "question": standardized_question,
            "correct_answer": correct_answer_data,
            "user_answer": None,
            "status": TaskStatus.PENDING.value,
            "result": None,
            "remark": None,
            "total_score": 10,
            "scored": 0,
            "submitted": False,
            "submitted_at": None,
            "attempts_count": 0,
            "difficulty_level": task_data.get("difficulty_level", 1),
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
            "input_type": self._get_input_type(processing_type),
            "metadata": {
                "_priority": priority,
                "_media_ready": False,
                "_options_audio_ready": False,
                "status": "generating",  # Universal root status
                "processing_type": processing_type
            }
        }
        
        # Add story data if present
        if "story" in task_data:
            task_item["story"] = self._format_story_data(task_data["story"])
        
        # Add media metadata fields based on task type
        if task_type in ["image_identification", "audio_identification"]:
            task_item["image_metadata"] = {}
            task_item["audio_metadata"] = {}
        elif task_type == "speak_word":
            task_item["audio_metadata"] = {}
        
        return task_item
    
    def format_story_item(
        self,
        story_data: Dict[str, Any],
        story_set_id: ObjectId,
        processing_type: Literal['primary', 'followup', 'curated'],
        stage: int = 1
    ) -> Dict[str, Any]:
        """
        Format a story item with consistent structure across all services.
        
        Args:
            story_data: Raw story data from AI generation
            story_set_id: Parent story set ID
            processing_type: Type of processing ('primary', 'followup', or 'curated')
            stage: Story stage number
            
        Returns:
            Formatted story item dictionary
        """
        story_id = ObjectId()
        
        story_item = {
            "_id": story_id,
            "story_set_id": story_set_id,
            "user_id": ObjectId(self.current_user.user.id),
            "stage": stage,
            "script": story_data.get("script", ""),
            "image": story_data.get("image", ""),
            "thumbnail": story_data.get("thumbnail", "📖"),
            "audio_metadata": {},
            "image_metadata": {},
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
            "input_type": self._get_input_type(processing_type),
            "metadata": {
                "status": "generating",  # Universal root status
                "processing_type": processing_type,
                "_media_ready": False,
                "audio_status": "pending",
                "image_status": "pending"
            }
        }
        
        return story_item
    
    def format_task_set(
        self,
        title: str,
        tasks: List[Dict[str, Any]],
        stories: List[Dict[str, Any]],
        processing_type: Literal['primary', 'followup', 'curated'],
        session_id: Optional[str] = None,
        theme_id: Optional[str] = None,
        audio_storage_info: Optional[Dict[str, Any]] = None,
        thumbnail: Optional[str] = None,
        original_task_set_id: Optional[ObjectId] = None,
        followup_count: int = 0
    ) -> Dict[str, Any]:
        """
        Format a task set with consistent structure across all services.
        
        Args:
            title: Task set title
            tasks: List of task items
            stories: List of story items
            processing_type: Type of processing ('primary', 'followup', or 'curated')
            session_id: Optional session ID
            theme_id: Optional theme ID
            audio_storage_info: Optional audio storage information
            thumbnail: Optional thumbnail text
            original_task_set_id: Original task set ID for followups
            followup_count: Followup level count
            
        Returns:
            Formatted task set dictionary
        """
        task_set_id = ObjectId()
        
        # Determine generation type
        if processing_type == "followup":
            gentype = GenerationType.FOLLOWUP.value
        elif processing_type == "curated":
            gentype = GenerationType.CURATED.value
        else:
            gentype = GenerationType.PRIMARY.value
        
        task_set = {
            "_id": task_set_id,
            "user_id": ObjectId(self.current_user.user.id),
            "title": title,
            "thumbnail": thumbnail or "📚",
            "difficulty_level": "medium",
            "input_type": self._get_input_type(processing_type),
            "gentype": gentype,
            "status": TaskStatus.PENDING.value,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc),
            "tasks": [],  # List of string IDs (populated after saving tasks)
            "stories": [],  # List of string IDs (populated after saving stories)
            "total_tasks": len(tasks),
            "total_stories": len(stories),
            "attempted_tasks": 0,
            "total_verified": 0,
            "total_score": len(tasks) * 10,  # 10 points per task
            "scored": 0,
            "attempts_count": 0,
            "text_tasks_ready": 0,
            "media_tasks_pending": len(tasks),
            "has_follow_up": False,
            "metadata": {
                "processing_type": processing_type,
                "thumbnail_status": "pending"
            }
        }
        
        # Add session ID if provided
        if session_id:
            task_set["session_id"] = session_id
        
        # Add theme ID if provided
        if theme_id:
            task_set["theme_id"] = theme_id
        
        # Add input content if provided
        if audio_storage_info:
            task_set["input_content"] = audio_storage_info
        
        # Add followup-specific fields
        if processing_type == "followup":
            task_set["followup_count"] = followup_count
            task_set["original_task_set_id"] = original_task_set_id
        
        return task_set
    
    def _format_correct_answer(self, task_data: Dict[str, Any], task_type: str) -> Dict[str, Any]:
        """Format correct answer with consistent structure."""
        # Get answer from various possible locations
        answer_value = (
            task_data.get("correct_answer", {}).get("value") or
            task_data.get("question", {}).get("answer") or
            task_data.get("answer") or
            ""
        )
        
        # Determine answer type based on task type
        if task_type == "single_choice":
            answer_type = "single"
        elif task_type == "multiple_choice":
            answer_type = "multiple"
            # Ensure multiple choice answers are in list format
            if isinstance(answer_value, str):
                answer_value = [answer_value]
        elif task_type == "speak_word":
            answer_type = "speak"
        else:
            answer_type = "single"
        
        return {
            "value": answer_value,
            "type": answer_type
        }
    
    def _format_story_data(self, story_data: Dict[str, Any]) -> Dict[str, Any]:
        """Format story data with consistent structure."""
        return {
            "stage": story_data.get("stage", 1),
            "script": story_data.get("script", ""),
            "image": story_data.get("image", ""),
            "media_url": story_data.get("media_url", ""),
            "metadata": story_data.get("metadata", {})
        }
    
    def _get_input_type(self, processing_type: str) -> str:
        """Get input type based on processing type."""
        if processing_type == "curated":
            return InputType.TEXT.value
        else:
            return InputType.AUDIO.value

    async def save_task_set(
        self,
        task_set_data: Dict[str, Any],
        processing_type: Literal['primary', 'followup', 'curated']
    ) -> ObjectId:
        """
        Save task set to appropriate collection based on processing type.

        Args:
            task_set_data: Formatted task set data
            processing_type: Type of processing to determine collection

        Returns:
            Task set ObjectId
        """
        collection_name = self._get_task_set_collection(processing_type)

        try:
            await self.current_user.async_db[collection_name].insert_one(task_set_data)
            logger.info(f"✅ Saved {processing_type} task set to {collection_name}: {task_set_data['_id']}")
            return task_set_data["_id"]
        except Exception as e:
            logger.error(f"❌ Failed to save {processing_type} task set: {e}")
            raise

    async def save_task_items(
        self,
        task_items: List[Dict[str, Any]],
        processing_type: Literal['primary', 'followup', 'curated']
    ) -> List[ObjectId]:
        """
        Save task items to appropriate collection based on processing type.

        Args:
            task_items: List of formatted task items
            processing_type: Type of processing to determine collection

        Returns:
            List of task item ObjectIds
        """
        collection_name = self._get_task_items_collection(processing_type)

        try:
            if task_items:
                result = await self.current_user.async_db[collection_name].insert_many(task_items)
                task_ids = result.inserted_ids
                logger.info(f"✅ Saved {len(task_items)} {processing_type} task items to {collection_name}")
                return task_ids
            return []
        except Exception as e:
            logger.error(f"❌ Failed to save {processing_type} task items: {e}")
            raise

    async def save_story_items(
        self,
        story_items: List[Dict[str, Any]],
        processing_type: Literal['primary', 'followup', 'curated']
    ) -> List[ObjectId]:
        """
        Save story items to appropriate collection based on processing type.

        Args:
            story_items: List of formatted story items
            processing_type: Type of processing to determine collection

        Returns:
            List of story item ObjectIds
        """
        collection_name = self._get_story_collection(processing_type)

        try:
            if story_items:
                result = await self.current_user.async_db[collection_name].insert_many(story_items)
                story_ids = result.inserted_ids
                logger.info(f"✅ Saved {len(story_items)} {processing_type} story items to {collection_name}")
                return story_ids
            return []
        except Exception as e:
            logger.error(f"❌ Failed to save {processing_type} story items: {e}")
            raise

    async def update_task_set_with_ids(
        self,
        task_set_id: ObjectId,
        task_ids: List[ObjectId],
        story_ids: List[ObjectId],
        processing_type: Literal['primary', 'followup', 'curated']
    ):
        """
        Update task set with task and story IDs after saving items.

        Args:
            task_set_id: Task set ObjectId
            task_ids: List of task item ObjectIds
            story_ids: List of story item ObjectIds
            processing_type: Type of processing to determine collection
        """
        collection_name = self._get_task_set_collection(processing_type)

        try:
            # Convert ObjectIds to strings for storage
            task_id_strings = [str(task_id) for task_id in task_ids]
            story_id_strings = [str(story_id) for story_id in story_ids]

            await self.current_user.async_db[collection_name].update_one(
                {"_id": task_set_id},
                {
                    "$set": {
                        "tasks": task_id_strings,
                        "stories": story_id_strings,
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
            logger.info(f"✅ Updated {processing_type} task set {task_set_id} with {len(task_ids)} tasks and {len(story_ids)} stories")
        except Exception as e:
            logger.error(f"❌ Failed to update {processing_type} task set with IDs: {e}")
            raise

    def _get_task_set_collection(self, processing_type: str) -> str:
        """Get task set collection name based on processing type."""
        if processing_type == "curated":
            return "curated_content_set"
        elif processing_type == "followup":
            return "task_sets"  # Followups use same collection as primary
        else:
            return "task_sets"

    def _get_task_items_collection(self, processing_type: str) -> str:
        """Get task items collection name based on processing type."""
        if processing_type == "curated":
            return "curated_content_items"
        elif processing_type == "followup":
            return "task_items"  # Followups use same collection as primary
        else:
            return "task_items"

    def _get_story_collection(self, processing_type: str) -> str:
        """Get story collection name based on processing type."""
        if processing_type == "curated":
            return "curated_content_story"
        else:
            return "story_steps"

    async def process_and_save_content(
        self,
        title: str,
        tasks_data: List[Dict[str, Any]],
        stories_data: List[Dict[str, Any]],
        processing_type: Literal['primary', 'followup', 'curated'],
        session_id: Optional[str] = None,
        theme_id: Optional[str] = None,
        audio_storage_info: Optional[Dict[str, Any]] = None,
        thumbnail: Optional[str] = None,
        original_task_set_id: Optional[ObjectId] = None,
        followup_count: int = 0
    ) -> Tuple[ObjectId, List[ObjectId], List[ObjectId]]:
        """
        Process and save complete content set (task set, tasks, and stories).

        Args:
            title: Task set title
            tasks_data: Raw task data from AI generation
            stories_data: Raw story data from AI generation
            processing_type: Type of processing ('primary', 'followup', or 'curated')
            session_id: Optional session ID
            theme_id: Optional theme ID
            audio_storage_info: Optional audio storage information
            thumbnail: Optional thumbnail text
            original_task_set_id: Original task set ID for followups
            followup_count: Followup level count

        Returns:
            Tuple of (task_set_id, task_ids, story_ids)
        """
        logger.info(f"🔄 Processing {processing_type} content: {len(tasks_data)} tasks, {len(stories_data)} stories")

        try:
            # Format task set
            task_set_data = self.format_task_set(
                title=title,
                tasks=tasks_data,
                stories=stories_data,
                processing_type=processing_type,
                session_id=session_id,
                theme_id=theme_id,
                audio_storage_info=audio_storage_info,
                thumbnail=thumbnail,
                original_task_set_id=original_task_set_id,
                followup_count=followup_count
            )

            task_set_id = task_set_data["_id"]

            # Format task items
            formatted_tasks = []
            for i, task_data in enumerate(tasks_data):
                formatted_task = self.format_task_item(
                    task_data=task_data,
                    task_set_id=task_set_id,
                    processing_type=processing_type,
                    priority=i
                )
                formatted_tasks.append(formatted_task)

            # Format story items
            formatted_stories = []
            for i, story_data in enumerate(stories_data):
                formatted_story = self.format_story_item(
                    story_data=story_data,
                    story_set_id=task_set_id,
                    processing_type=processing_type,
                    stage=i + 1
                )
                formatted_stories.append(formatted_story)

            # Save to database
            task_set_id = await self.save_task_set(task_set_data, processing_type)
            task_ids = await self.save_task_items(formatted_tasks, processing_type)
            story_ids = await self.save_story_items(formatted_stories, processing_type)

            # Update task set with IDs
            await self.update_task_set_with_ids(task_set_id, task_ids, story_ids, processing_type)

            logger.info(f"✅ Successfully processed {processing_type} content set: {task_set_id}")
            return task_set_id, task_ids, story_ids

        except Exception as e:
            logger.error(f"❌ Failed to process {processing_type} content: {e}")
            raise


# Convenience functions for easy integration
async def process_primary_content(
    current_user: UserTenantDB,
    title: str,
    tasks_data: List[Dict[str, Any]],
    stories_data: List[Dict[str, Any]],
    session_id: str,
    audio_storage_info: Optional[Dict[str, Any]] = None,
    thumbnail: Optional[str] = None
) -> Tuple[ObjectId, List[ObjectId], List[ObjectId]]:
    """
    Convenience function to process primary content using the unified processor.

    Args:
        current_user: User context with database access
        title: Task set title
        tasks_data: Raw task data from AI generation
        stories_data: Raw story data from AI generation
        session_id: Session ID
        audio_storage_info: Optional audio storage information
        thumbnail: Optional thumbnail text

    Returns:
        Tuple of (task_set_id, task_ids, story_ids)
    """
    processor = UnifiedContentProcessor(current_user)
    return await processor.process_and_save_content(
        title=title,
        tasks_data=tasks_data,
        stories_data=stories_data,
        processing_type="primary",
        session_id=session_id,
        audio_storage_info=audio_storage_info,
        thumbnail=thumbnail
    )


async def process_followup_content(
    current_user: UserTenantDB,
    title: str,
    tasks_data: List[Dict[str, Any]],
    original_task_set_id: ObjectId,
    followup_count: int
) -> Tuple[ObjectId, List[ObjectId], List[ObjectId]]:
    """
    Convenience function to process followup content using the unified processor.

    Args:
        current_user: User context with database access
        title: Task set title
        tasks_data: Raw task data from AI generation
        original_task_set_id: Original task set ID
        followup_count: Followup level count

    Returns:
        Tuple of (task_set_id, task_ids, story_ids)
    """
    processor = UnifiedContentProcessor(current_user)
    return await processor.process_and_save_content(
        title=title,
        tasks_data=tasks_data,
        stories_data=[],  # Followups don't have stories
        processing_type="followup",
        original_task_set_id=original_task_set_id,
        followup_count=followup_count
    )


async def process_curated_content(
    current_user: UserTenantDB,
    title: str,
    tasks_data: List[Dict[str, Any]],
    stories_data: List[Dict[str, Any]],
    theme_id: Optional[str] = None,
    thumbnail: Optional[str] = None
) -> Tuple[ObjectId, List[ObjectId], List[ObjectId]]:
    """
    Convenience function to process curated content using the unified processor.

    Args:
        current_user: User context with database access
        title: Task set title
        tasks_data: Raw task data from AI generation
        stories_data: Raw story data from AI generation
        theme_id: Optional theme ID
        thumbnail: Optional thumbnail text

    Returns:
        Tuple of (task_set_id, task_ids, story_ids)
    """
    processor = UnifiedContentProcessor(current_user)
    return await processor.process_and_save_content(
        title=title,
        tasks_data=tasks_data,
        stories_data=stories_data,
        processing_type="curated",
        theme_id=theme_id,
        thumbnail=thumbnail
    )
