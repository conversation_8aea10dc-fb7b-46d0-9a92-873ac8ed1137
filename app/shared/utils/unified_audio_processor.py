"""
Unified Audio Processing System for Socket Service, Followup Generation, and Editor Service

This module provides a shared audio processing utility that handles:
- Options audio generation (for single choice and multiple choice tasks)
- Task-specific audio generation (for audio/image tasks)
- Story audio generation

Features:
- Sequential processing to avoid rate limiting
- EdgeTTS integration for all audio generation
- Media caching with multi-user support
- Universal metadata status tracking
- Proper error handling and status updates
"""

import asyncio
import hashlib
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List, Optional, Literal
from bson import ObjectId

from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from app.v2.api.socket_service_v2.generator.edgettsgen import generate_audio_bytes
from app.shared.async_minio_client import create_async_minio_client

logger = setup_new_logging(__name__)


class UnifiedAudioProcessor:
    """
    Unified audio processing system for all services.
    
    This class handles audio generation for tasks, options, and stories
    across socket service, followup generation, and editor service.
    """
    
    def __init__(self, current_user: UserTenantDB):
        """
        Initialize the unified audio processor.
        
        Args:
            current_user: User context with database and MinIO access
        """
        self.current_user = current_user
        self.async_minio_client = create_async_minio_client(current_user.minio)
    
    async def process_tasks_audio(
        self,
        tasks: List[Dict[str, Any]],
        collection_name: str,
        processing_type: Literal['primary', 'followup', 'curated'],
        delay_between_options: float = 2.0
    ) -> Dict[str, Any]:
        """
        Process audio generation for a list of tasks.
        
        Args:
            tasks: List of task items to process
            collection_name: Database collection name (e.g., 'task_items', 'followup_task_items', 'curated_content_items')
            processing_type: Type of processing ('primary', 'followup', or 'curated')
            delay_between_options: Delay in seconds between options audio generation
            
        Returns:
            Dictionary with processing results and statistics
        """
        logger.info(f"🎵 Starting {processing_type} audio processing for {len(tasks)} tasks")
        
        results = {
            "processed_tasks": 0,
            "successful_tasks": 0,
            "failed_tasks": 0,
            "options_generated": 0,
            "task_audio_generated": 0,
            "errors": []
        }
        
        for task in tasks:
            try:
                task_id = task.get("_id")
                task_type = task.get("type", "")
                
                logger.info(f"🎵 Processing {processing_type} task {task_id} | Type: {task_type}")
                
                # Set initial status to generating
                await self._update_task_status(task_id, collection_name, "generating")
                
                # Process based on task type
                if task_type in ["single_choice", "multiple_choice"]:
                    await self._process_options_audio(task, task_id, collection_name, delay_between_options)
                    results["options_generated"] += len(task.get("question", {}).get("options", {}))
                    
                elif task_type in ["speak_word", "audio_identification"]:
                    await self._process_task_audio(task, task_id, collection_name)
                    results["task_audio_generated"] += 1
                    
                elif task_type == "image_identification":
                    # For image identification, only process options audio
                    await self._process_options_audio(task, task_id, collection_name, delay_between_options)
                    results["options_generated"] += len(task.get("question", {}).get("options", {}))
                
                # Update status to completed
                await self._update_task_status(task_id, collection_name, "completed")
                results["successful_tasks"] += 1
                
            except Exception as e:
                error_msg = f"Failed to process task {task.get('_id')}: {str(e)}"
                logger.error(f"❌ {error_msg}")
                results["errors"].append(error_msg)
                results["failed_tasks"] += 1
                
                # Update status to failed
                try:
                    await self._update_task_status(task.get("_id"), collection_name, "failed", str(e))
                except Exception as status_error:
                    logger.error(f"❌ Failed to update task status: {status_error}")
            
            results["processed_tasks"] += 1
        
        logger.info(f"✅ {processing_type} audio processing completed: {results['successful_tasks']}/{results['processed_tasks']} successful")
        return results
    
    async def process_stories_audio(
        self,
        stories: List[Dict[str, Any]],
        collection_name: str,
        processing_type: Literal['primary', 'followup', 'curated']
    ) -> Dict[str, Any]:
        """
        Process audio generation for a list of stories.
        
        Args:
            stories: List of story items to process
            collection_name: Database collection name (e.g., 'story_steps', 'curated_content_story')
            processing_type: Type of processing ('primary', 'followup', or 'curated')
            
        Returns:
            Dictionary with processing results and statistics
        """
        logger.info(f"🎵 Starting {processing_type} story audio processing for {len(stories)} stories")
        
        results = {
            "processed_stories": 0,
            "successful_stories": 0,
            "failed_stories": 0,
            "errors": []
        }
        
        for story in stories:
            try:
                story_id = story.get("_id")
                script = story.get("script", "")
                
                if not script:
                    logger.warning(f"⚠️ Story {story_id} has no script, skipping audio generation")
                    continue
                
                logger.info(f"🎵 Processing {processing_type} story {story_id}")
                
                # Set initial status to generating
                await self._update_story_status(story_id, collection_name, "generating")
                
                # Generate story audio
                await self._process_story_audio(story, story_id, collection_name)
                
                # Update status to completed
                await self._update_story_status(story_id, collection_name, "completed")
                results["successful_stories"] += 1
                
            except Exception as e:
                error_msg = f"Failed to process story {story.get('_id')}: {str(e)}"
                logger.error(f"❌ {error_msg}")
                results["errors"].append(error_msg)
                results["failed_stories"] += 1
                
                # Update status to failed
                try:
                    await self._update_story_status(story.get("_id"), collection_name, "failed", str(e))
                except Exception as status_error:
                    logger.error(f"❌ Failed to update story status: {status_error}")
            
            results["processed_stories"] += 1
        
        logger.info(f"✅ {processing_type} story audio processing completed: {results['successful_stories']}/{results['processed_stories']} successful")
        return results

    async def _process_options_audio(
        self,
        task: Dict[str, Any],
        task_id: ObjectId,
        collection_name: str,
        delay_between_options: float
    ):
        """
        Process options audio generation for a task.

        Args:
            task: Task dictionary containing question and options
            task_id: Task ObjectId
            collection_name: Database collection name
            delay_between_options: Delay in seconds between options generation
        """
        options = task.get("question", {}).get("options", {})
        if not options:
            logger.warning(f"⚠️ Task {task_id} has no options, skipping options audio generation")
            return

        total_options = len(options)
        options_metadata = {}

        logger.info(f"🎵 Generating options audio for task {task_id} | {total_options} options")

        # Set options audio status to generating
        await self._update_task_field(task_id, collection_name, {
            "metadata.options_audio_status": "generating",
            "updated_at": datetime.now(timezone.utc)
        })

        # Process each option sequentially
        for i, (option_key, option_value) in enumerate(options.items(), 1):
            try:
                logger.info(f"🎵 OPTION {i}/{total_options} | TASK {task_id} | PROCESSING: {option_key}")

                # Generate audio for this option
                audio_url, file_info = await self._generate_single_option_audio(
                    str(option_value), task_id, option_key, i, total_options
                )

                options_metadata[option_key] = {
                    "text": str(option_value),
                    "audio_url": audio_url,
                    "file_info": file_info,
                    "cache_id": f"{str(option_value).strip().lower()}_audio",
                    "generated_at": datetime.now(timezone.utc).isoformat()
                }

                # Add delay between options to avoid rate limiting
                if i < total_options and delay_between_options > 0:
                    logger.info(f"⏳ Waiting {delay_between_options}s before next option...")
                    await asyncio.sleep(delay_between_options)

            except Exception as e:
                logger.error(f"❌ OPTION {i}/{total_options} | TASK {task_id} | ERROR: {str(e)}")
                options_metadata[option_key] = {
                    "text": str(option_value),
                    "audio_url": None,
                    "file_info": None,
                    "cache_id": f"{str(option_value).strip().lower()}_audio",
                    "error": str(e),
                    "generated_at": datetime.now(timezone.utc).isoformat()
                }

        # Update task with options metadata
        await self._update_task_field(task_id, collection_name, {
            "question.options_metadata": options_metadata,
            "metadata.options_audio_status": "completed",
            "updated_at": datetime.now(timezone.utc)
        })

        logger.info(f"✅ Options audio generation completed for task {task_id}")

    async def _process_task_audio(
        self,
        task: Dict[str, Any],
        task_id: ObjectId,
        collection_name: str
    ):
        """
        Process task-specific audio generation (for speak_word, audio_identification tasks).

        Args:
            task: Task dictionary
            task_id: Task ObjectId
            collection_name: Database collection name
        """
        # Get the keyword for audio generation
        keyword = None
        if task.get("type") == "speak_word":
            keyword = task.get("question", {}).get("text", "")
        elif task.get("type") == "audio_identification":
            keyword = task.get("question", {}).get("text", "")

        if not keyword:
            logger.warning(f"⚠️ Task {task_id} has no keyword for audio generation")
            return

        logger.info(f"🔊 Generating task audio for {task_id} | Keyword: {keyword}")

        # Set audio status to generating
        await self._update_task_field(task_id, collection_name, {
            "metadata.audio_status": "generating",
            "updated_at": datetime.now(timezone.utc)
        })

        try:
            # Generate audio
            file_info = await self._generate_audio_with_caching(keyword, "audio_prompt")

            if file_info:
                # Update task with audio metadata
                await self._update_task_field(task_id, collection_name, {
                    "question.metadata": file_info,
                    "metadata.audio_status": "completed",
                    "metadata._media_ready": True,
                    "updated_at": datetime.now(timezone.utc)
                })

                logger.info(f"✅ Task audio generation completed for {task_id}")
            else:
                raise Exception("Audio generation failed - no file info returned")

        except Exception as e:
            logger.error(f"❌ Task audio generation failed for {task_id}: {str(e)}")
            await self._update_task_field(task_id, collection_name, {
                "metadata.audio_status": "failed",
                "metadata.audio_error": str(e),
                "updated_at": datetime.now(timezone.utc)
            })
            raise

    async def _process_story_audio(
        self,
        story: Dict[str, Any],
        story_id: ObjectId,
        collection_name: str
    ):
        """
        Process story audio generation.

        Args:
            story: Story dictionary containing script
            story_id: Story ObjectId
            collection_name: Database collection name
        """
        script = story.get("script", "")
        if not script:
            logger.warning(f"⚠️ Story {story_id} has no script for audio generation")
            return

        logger.info(f"🔊 Generating story audio for {story_id}")

        try:
            # Generate audio
            file_info = await self._generate_audio_with_caching(script, "audio_prompt")

            if file_info:
                # Update story with audio metadata
                await self._update_story_field(story_id, collection_name, {
                    "audio_metadata": file_info,
                    "metadata._media_ready": True,
                    "metadata.audio_status": "completed",
                    "updated_at": datetime.now(timezone.utc)
                })

                logger.info(f"✅ Story audio generation completed for {story_id}")
            else:
                raise Exception("Audio generation failed - no file info returned")

        except Exception as e:
            logger.error(f"❌ Story audio generation failed for {story_id}: {str(e)}")
            await self._update_story_field(story_id, collection_name, {
                "metadata.audio_status": "failed",
                "metadata.audio_error": str(e),
                "updated_at": datetime.now(timezone.utc)
            })
            raise

    async def _generate_single_option_audio(
        self,
        option_text: str,
        task_id: ObjectId,
        option_key: str,  # Used for logging context
        option_number: int,
        total_options: int
    ) -> tuple[Optional[str], Optional[Dict[str, Any]]]:
        """
        Generate audio for a single option.

        Args:
            option_text: Text content of the option
            task_id: Task ObjectId for logging
            option_key: Option key (a, b, c, etc.)
            option_number: Current option number
            total_options: Total number of options

        Returns:
            Tuple of (audio_url, file_info)
        """
        try:
            # Check cache first
            cached_file_info = await self._check_media_cache(option_text, "audio")
            if cached_file_info:
                audio_url = cached_file_info.get("url")
                logger.info(f"🎯 OPTION {option_number}/{total_options} | TASK {task_id} | AUDIO: CACHED | URL: {audio_url}")
                return audio_url, cached_file_info

            # Generate new audio
            logger.info(f"🎵 OPTION {option_number}/{total_options} | TASK {task_id} | KEY: {option_key} | AUDIO: GENERATING | TEXT: {option_text}")
            file_info = await self._generate_audio_with_caching(option_text, "audio_prompt")

            if file_info and file_info.get("url"):
                audio_url = file_info.get("url")
                logger.info(f"✅ OPTION {option_number}/{total_options} | TASK {task_id} | AUDIO: GENERATED | URL: {audio_url}")
                return audio_url, file_info
            else:
                logger.error(f"❌ OPTION {option_number}/{total_options} | TASK {task_id} | AUDIO: NO_DATA")
                return None, None

        except Exception as e:
            logger.error(f"❌ OPTION {option_number}/{total_options} | TASK {task_id} | AUDIO: ERROR | {str(e)}")
            return None, None

    async def _generate_audio_with_caching(
        self,
        keyword: str,
        prompt_type: str = "audio_prompt"
    ) -> Optional[Dict[str, Any]]:
        """
        Generate audio with caching support.

        Args:
            keyword: Text to convert to audio
            prompt_type: Type of prompt for caching

        Returns:
            File info dictionary or None if generation failed
        """
        # Check cache first
        cached_file_info = await self._check_media_cache(keyword, "audio")
        if cached_file_info:
            logger.info(f"🎯 Using cached audio for keyword: {keyword}")
            return cached_file_info

        # Generate new audio using EdgeTTS
        try:
            file_bytes = await generate_audio_bytes(text=keyword)
            if not file_bytes:
                logger.error(f"No audio data received for: {keyword}")
                return None

            # Detect audio format
            file_extension, content_type = self._detect_audio_format(file_bytes)
            if not file_extension:
                raise ValueError("Unsupported audio format")

            # Save to MinIO
            file_info = await self.async_minio_client.save_file_async(
                data=file_bytes,
                user_id=self.current_user.user.id,
                content_type=content_type,
                folder="audiogen",
                file_extension=file_extension,
                custom_filename=hashlib.sha256(f"{keyword}_audio{file_extension}".encode()).hexdigest()[:10] + file_extension
            )

            # Cache the generated audio
            await self._save_media_cache(keyword, "audio", prompt_type, "", file_info, {})

            logger.info(f"Audio generated and saved: {keyword}")
            return file_info

        except Exception as e:
            logger.error(f"Audio generation failed for '{keyword}': {str(e)}")
            return None

    def _detect_audio_format(self, file_bytes: bytes) -> tuple[Optional[str], Optional[str]]:
        """
        Detect audio format from file bytes.

        Args:
            file_bytes: Audio file bytes

        Returns:
            Tuple of (file_extension, content_type)
        """
        if file_bytes.startswith(b'RIFF') and file_bytes[8:12] == b'WAVE':
            return ".wav", "audio/wav"
        elif file_bytes.startswith(b'ID3') or file_bytes[0:2] == b'\xFF\xFB':
            return ".mp3", "audio/mpeg"
        elif file_bytes[:2] in [b'\xFF\xFB', b'\xFF\xF3', b'\xFF\xF2']:  # Raw MP3 frames
            return ".mp3", "audio/mpeg"
        elif file_bytes.startswith(b'OggS'):
            return ".ogg", "audio/ogg"
        elif file_bytes.startswith(b'\x1A\x45\xDF\xA3'):  # EBML header (used in WebM and MKV)
            return ".webm", "audio/webm"
        else:
            logger.error(f"Unsupported audio format detected")
            return None, None

    async def _check_media_cache(self, keyword: str, media_type: str) -> Optional[Dict[str, Any]]:
        """
        Check if media already exists in cache.

        Args:
            keyword: The keyword/text used to generate media
            media_type: Type of media ("audio" or "image")

        Returns:
            File info dictionary if found, None otherwise
        """
        try:
            media_collection = self.current_user.db["media"]
            cached_item = media_collection.find_one({
                "keyword": keyword.strip().lower(),
                "media_type": media_type
            })

            if cached_item:
                # Update user access tracking
                user_ids = cached_item.get("user_ids", [])
                if self.current_user.user.id not in user_ids:
                    user_ids.append(self.current_user.user.id)
                    media_collection.update_one(
                        {"_id": cached_item["_id"]},
                        {
                            "$set": {
                                "user_ids": user_ids,
                                "last_accessed_at": datetime.now(timezone.utc),
                                "last_accessed_by": self.current_user.user.id
                            }
                        }
                    )
                else:
                    # Just update last access time
                    media_collection.update_one(
                        {"_id": cached_item["_id"]},
                        {
                            "$set": {
                                "last_accessed_at": datetime.now(timezone.utc),
                                "last_accessed_by": self.current_user.user.id
                            }
                        }
                    )

                # Generate fresh presigned URL
                try:
                    presigned_url = self.current_user.minio.get_presigned_url(
                        bucket_name=self.current_user.minio_bucket_name,
                        object_name=cached_item["object_name"],
                        expires=timedelta(hours=24),
                        method="GET"
                    )

                    # Update file_info with fresh URL
                    file_info = cached_item["file_info"].copy()
                    file_info["url"] = presigned_url
                    return file_info

                except Exception as url_error:
                    logger.error(f"❌ Error generating presigned URL for cached media: {url_error}")
                    return None

            return None
        except Exception as e:
            logger.warning(f"Cache check failed: {e}")
            return None

    async def _save_media_cache(
        self,
        keyword: str,
        media_type: str,
        prompt_type: str,
        file_text: str,
        file_info: Dict[str, Any],
        usage_metadata: Dict[str, Any]
    ):
        """
        Save media to cache.

        Args:
            keyword: The keyword/text used to generate media
            media_type: Type of media ("audio" or "image")
            prompt_type: Type of prompt used
            file_text: Generated text content
            file_info: File information from MinIO
            usage_metadata: API usage metadata
        """
        try:
            # Create a unique cache key
            cache_key = hashlib.sha256(f"{keyword}_{media_type}_{prompt_type}".encode()).hexdigest()

            # Check if media already exists
            existing_media = await self.current_user.async_db.media.find_one({
                "cache_key": cache_key
            })

            if existing_media:
                # Update user access tracking
                user_ids = existing_media.get("user_ids", [])
                if self.current_user.user.id not in user_ids:
                    user_ids.append(self.current_user.user.id)
                    await self.current_user.async_db.media.update_one(
                        {"_id": existing_media["_id"]},
                        {
                            "$set": {
                                "user_ids": user_ids,
                                "last_accessed_at": datetime.now(timezone.utc),
                                "last_accessed_by": self.current_user.user.id
                            }
                        }
                    )
                logger.debug(f"Updated user access for cached {media_type} for keyword: {keyword}")
            else:
                # Create new media cache entry
                cache_document = {
                    "cache_key": cache_key,
                    "keyword": keyword.strip().lower(),
                    "media_type": media_type,
                    "prompt_type": prompt_type,
                    "file_text": file_text,
                    "file_info": file_info,
                    "usage_metadata": usage_metadata,
                    "object_name": file_info.get("object_name"),
                    "created_at": datetime.now(timezone.utc),
                    "user_ids": [self.current_user.user.id],
                    "created_by": self.current_user.user.id,
                    "last_accessed_at": datetime.now(timezone.utc),
                    "last_accessed_by": self.current_user.user.id
                }

                await self.current_user.async_db.media.update_one(
                    {"cache_key": cache_key},
                    {"$set": cache_document},
                    upsert=True
                )
                logger.info(f"💾 Cached {media_type} for keyword: {keyword} with multi-user support")

        except Exception as e:
            logger.error(f"❌ Error saving media cache: {e}")
            # Don't fail the main operation if caching fails

    async def _update_task_status(
        self,
        task_id: ObjectId,
        collection_name: str,
        status: str,
        error_message: Optional[str] = None
    ):
        """
        Update task status in the database.

        Args:
            task_id: Task ObjectId
            collection_name: Database collection name
            status: Status to set ('generating', 'completed', 'failed')
            error_message: Optional error message for failed status
        """
        update_fields = {
            "metadata.status": status,
            "updated_at": datetime.now(timezone.utc)
        }

        if error_message:
            update_fields["metadata.error"] = error_message

        await self.current_user.async_db[collection_name].update_one(
            {"_id": task_id},
            {"$set": update_fields}
        )

    async def _update_task_field(
        self,
        task_id: ObjectId,
        collection_name: str,
        update_fields: Dict[str, Any]
    ):
        """
        Update specific fields in a task.

        Args:
            task_id: Task ObjectId
            collection_name: Database collection name
            update_fields: Dictionary of fields to update
        """
        await self.current_user.async_db[collection_name].update_one(
            {"_id": task_id},
            {"$set": update_fields}
        )

    async def _update_story_status(
        self,
        story_id: ObjectId,
        collection_name: str,
        status: str,
        error_message: Optional[str] = None
    ):
        """
        Update story status in the database.

        Args:
            story_id: Story ObjectId
            collection_name: Database collection name
            status: Status to set ('generating', 'completed', 'failed')
            error_message: Optional error message for failed status
        """
        update_fields = {
            "metadata.audio_status": status,
            "updated_at": datetime.now(timezone.utc)
        }

        if error_message:
            update_fields["metadata.audio_error"] = error_message

        await self.current_user.async_db[collection_name].update_one(
            {"_id": story_id},
            {"$set": update_fields}
        )

    async def _update_story_field(
        self,
        story_id: ObjectId,
        collection_name: str,
        update_fields: Dict[str, Any]
    ):
        """
        Update specific fields in a story.

        Args:
            story_id: Story ObjectId
            collection_name: Database collection name
            update_fields: Dictionary of fields to update
        """
        await self.current_user.async_db[collection_name].update_one(
            {"_id": story_id},
            {"$set": update_fields}
        )


# Convenience functions for easy integration
async def process_tasks_audio(
    current_user: UserTenantDB,
    tasks: List[Dict[str, Any]],
    collection_name: str,
    processing_type: Literal['primary', 'followup', 'curated'],
    delay_between_options: float = 2.0
) -> Dict[str, Any]:
    """
    Convenience function to process tasks audio using the unified processor.

    Args:
        current_user: User context with database and MinIO access
        tasks: List of task items to process
        collection_name: Database collection name
        processing_type: Type of processing ('primary', 'followup', or 'curated')
        delay_between_options: Delay in seconds between options audio generation

    Returns:
        Dictionary with processing results and statistics
    """
    processor = UnifiedAudioProcessor(current_user)
    return await processor.process_tasks_audio(tasks, collection_name, processing_type, delay_between_options)


async def process_stories_audio(
    current_user: UserTenantDB,
    stories: List[Dict[str, Any]],
    collection_name: str,
    processing_type: Literal['primary', 'followup', 'curated']
) -> Dict[str, Any]:
    """
    Convenience function to process stories audio using the unified processor.

    Args:
        current_user: User context with database and MinIO access
        stories: List of story items to process
        collection_name: Database collection name
        processing_type: Type of processing ('primary', 'followup', or 'curated')

    Returns:
        Dictionary with processing results and statistics
    """
    processor = UnifiedAudioProcessor(current_user)
    return await processor.process_stories_audio(stories, collection_name, processing_type)
