"""
Universal Task Generation System

This module provides a completely standardized task generation interface that all services
(socket, followup, editor) must use. It defines fixed item keys and structure that must
be followed regardless of the AI model used.

Key Features:
- Fixed standardized keys for all task properties
- Unified response format from all AI models
- Consistent data structure across all services
- Single source of truth for task formatting
"""

from datetime import datetime, timezone
from typing import Dict, Any, List, Optional, Literal, Union
from bson import ObjectId
from dataclasses import dataclass
from enum import Enum

from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)


class TaskType(Enum):
    """Standardized task types across all services."""
    SINGLE_CHOICE = "single_choice"
    MULTIPLE_CHOICE = "multiple_choice"
    IMAGE_IDENTIFICATION = "image_identification"
    SPEAK_WORD = "speak_word"
    AUDIO_IDENTIFICATION = "audio_identification"


class AnswerType(Enum):
    """Standardized answer types."""
    SINGLE = "single"
    MULTIPLE = "multiple"
    SPEAK = "speak"


@dataclass
class StandardizedQuestion:
    """Standardized question structure with fixed keys."""
    text: str                           # Main question text in Nepali
    translated_text: str                # English translation
    options: Dict[str, str]            # Options dictionary {"a": "option1", "b": "option2"}
    answer_hint: str                   # Hint for the answer
    media_keyword: Optional[str] = None # Keyword for media generation
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary with fixed keys."""
        return {
            "text": self.text,
            "translated_text": self.translated_text,
            "options": self.options,
            "answer_hint": self.answer_hint,
            "media_keyword": self.media_keyword,
            "options_metadata": {},  # Will be populated during audio generation
            "metadata": {}           # Will be populated during media generation
        }


@dataclass
class StandardizedAnswer:
    """Standardized answer structure with fixed keys."""
    value: Union[str, List[str]]  # Answer value(s)
    type: AnswerType             # Answer type
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary with fixed keys."""
        return {
            "value": self.value,
            "type": self.type.value
        }


@dataclass
class StandardizedTask:
    """Standardized task structure with fixed keys."""
    type: TaskType                    # Task type
    title: str                       # Task title
    question: StandardizedQuestion   # Question data
    correct_answer: StandardizedAnswer # Correct answer data
    story: Optional[Dict[str, Any]] = None  # Optional story data
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary with fixed keys."""
        task_dict = {
            "type": self.type.value,
            "title": self.title,
            "question": self.question.to_dict(),
            "correct_answer": self.correct_answer.to_dict()
        }
        
        if self.story:
            task_dict["story"] = self.story
            
        return task_dict


@dataclass
class StandardizedStory:
    """Standardized story structure with fixed keys."""
    script: str                      # Story script text
    image_keyword: str              # Keyword for image generation
    stage: int = 1                  # Story stage number
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary with fixed keys."""
        return {
            "script": self.script,
            "image_keyword": self.image_keyword,
            "stage": self.stage,
            "audio_metadata": {},  # Will be populated during audio generation
            "image_metadata": {}   # Will be populated during image generation
        }


@dataclass
class StandardizedTaskSet:
    """Standardized task set structure with fixed keys."""
    title: str                       # Task set title
    thumbnail_keyword: str           # Keyword for thumbnail generation
    tasks: List[StandardizedTask]    # List of tasks
    stories: List[StandardizedStory] # List of stories
    description: Optional[str] = None        # Optional description
    description_en: Optional[str] = None     # Optional English description
    engagement_questions: List[str] = None   # Optional engagement questions
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary with fixed keys."""
        return {
            "title": self.title,
            "thumbnail_keyword": self.thumbnail_keyword,
            "description": self.description or "",
            "description_en": self.description_en or "",
            "engagement_questions": self.engagement_questions or [],
            "tasks": [task.to_dict() for task in self.tasks],
            "stories": [story.to_dict() for story in self.stories]
        }


class UniversalTaskGenerator:
    """
    Universal task generator that provides standardized interface for all services.
    
    This class ensures that regardless of which AI model is used (audio-based, Gemini, 
    context-based), the output always follows the same standardized format with fixed keys.
    """
    
    def __init__(self, current_user: UserTenantDB):
        """Initialize the universal task generator."""
        self.current_user = current_user
    
    def parse_ai_response(
        self,
        ai_response: Dict[str, Any],
        source_type: Literal['socket', 'followup', 'editor']
    ) -> StandardizedTaskSet:
        """
        Parse AI response from any source into standardized format.
        
        Args:
            ai_response: Raw AI response from any model
            source_type: Source of the response ('socket', 'followup', 'editor')
            
        Returns:
            StandardizedTaskSet with fixed keys and structure
        """
        logger.info(f"🔄 Parsing {source_type} AI response into standardized format")
        
        # Extract basic information with fallbacks
        title = self._extract_title(ai_response, source_type)
        thumbnail_keyword = self._extract_thumbnail(ai_response, title)
        description = self._extract_description(ai_response)
        description_en = self._extract_description_en(ai_response)
        engagement_questions = self._extract_engagement_questions(ai_response)
        
        # Extract and standardize tasks
        raw_tasks = self._extract_raw_tasks(ai_response, source_type)
        standardized_tasks = []
        
        for i, raw_task in enumerate(raw_tasks):
            try:
                standardized_task = self._standardize_task(raw_task, i)
                standardized_tasks.append(standardized_task)
            except Exception as e:
                logger.error(f"❌ Failed to standardize task {i}: {e}")
                continue
        
        # Extract and standardize stories
        raw_stories = self._extract_raw_stories(ai_response, raw_tasks)
        standardized_stories = []
        
        for i, raw_story in enumerate(raw_stories):
            try:
                standardized_story = self._standardize_story(raw_story, i)
                standardized_stories.append(standardized_story)
            except Exception as e:
                logger.error(f"❌ Failed to standardize story {i}: {e}")
                continue
        
        # Create standardized task set
        task_set = StandardizedTaskSet(
            title=title,
            thumbnail_keyword=thumbnail_keyword,
            tasks=standardized_tasks,
            stories=standardized_stories,
            description=description,
            description_en=description_en,
            engagement_questions=engagement_questions
        )
        
        logger.info(f"✅ Standardized {source_type} response: {len(standardized_tasks)} tasks, {len(standardized_stories)} stories")
        return task_set
